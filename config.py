#!/usr/bin/env python3
"""
Configuration settings for Video Downloader Tool
"""

import os
from pathlib import Path

# Default settings
DEFAULT_CONFIG = {
    # Download settings
    'default_output_dir': 'downloads',
    'default_quality': 'Full HD',  # Only Full HD and HD options available
    'default_format': 'mp4',  # Only MP4 format
    'default_audio_format': 'mp3',
    'default_audio_quality': '192',

    # File naming
    'filename_template': '%(title)s.%(ext)s',
    'sanitize_filenames': True,
    'max_filename_length': 200,

    # Download behavior
    'continue_on_error': True,
    'overwrite_existing': False,
    'create_subdirs': False,
    'extract_audio': False,

    # Network settings
    'max_retries': 3,
    'retry_delay': 1,
    'timeout': 30,
    'concurrent_downloads': 1,

    # Proxy settings (optional)
    'use_proxy': False,
    'proxy_url': '',
    'proxy_username': '',
    'proxy_password': '',

    # Platform-specific settings
    'youtube_api_key': '',  # Optional for enhanced YouTube support
    'cookies_file': '',     # Path to cookies file for authenticated downloads

    # GUI settings
    'window_width': 800,
    'window_height': 700,
    'theme': 'clam',
    'auto_paste_url': True,
    'show_progress_details': True,

    # Completion settings
    'auto_close_on_complete': False,
    'auto_close_delay': 3,  # seconds
    'show_completion_notification': True,
    'play_completion_sound': True,
    'open_output_folder_on_complete': False,
    'show_completion_popup': True,
    'auto_clear_url_on_complete': True,

    # Logging
    'log_level': 'INFO',
    'log_to_file': False,
    'log_file': 'downloader.log',
    'max_log_size': 10,  # MB

    # Statistics
    'total_downloads': 0,  # Track total number of successful downloads
}

# Quality presets - WORKING FORMAT STRINGS FOR PROPER QUALITY
QUALITY_PRESETS = {
    # Full HD (1080p) - Target 1080p specifically, fallback to 720p, then best
    'Full HD': 'best[height=1080]/best[height=720]/bestvideo[height<=1080]+bestaudio/best',

    # HD (720p) - Target 720p specifically, fallback to best available
    'HD': 'best[height=720]/best[height=480]/bestvideo[height<=720]+bestaudio/best',

    # Legacy support for direct quality specifications
    '1080p': 'best[height=1080]/best[height=720]/bestvideo[height<=1080]+bestaudio/best',
    '720p': 'best[height=720]/best[height=480]/bestvideo[height<=720]+bestaudio/best',

    # Additional fallback options
    'best': 'best',
    'worst': 'worst',
}

# Format presets
FORMAT_PRESETS = {
    'mp4': 'mp4',
}

# Audio format presets
AUDIO_FORMAT_PRESETS = {
    'mp3': 'mp3',
    'aac': 'aac',
    'ogg': 'ogg',
    'wav': 'wav',
    'flac': 'flac',
    'm4a': 'm4a',
}

# Supported platforms and their specific settings
PLATFORM_SETTINGS = {
    'youtube': {
        'name': 'YouTube',
        'domains': ['youtube.com', 'youtu.be', 'm.youtube.com'],
        'supports_playlists': True,
        'supports_live': True,
        'max_quality': '4k',
        'audio_formats': ['mp3', 'aac', 'm4a'],
    },
    'tiktok': {
        'name': 'TikTok',
        'domains': ['tiktok.com', 'vm.tiktok.com'],
        'supports_playlists': False,
        'supports_live': False,
        'max_quality': '720p',
        'audio_formats': ['mp3', 'aac'],
    },
    'instagram': {
        'name': 'Instagram',
        'domains': ['instagram.com'],
        'supports_playlists': False,
        'supports_live': True,
        'max_quality': '1080p',
        'audio_formats': ['mp3', 'aac'],
    },
    'twitter': {
        'name': 'Twitter/X',
        'domains': ['twitter.com', 'x.com', 't.co'],
        'supports_playlists': False,
        'supports_live': True,
        'max_quality': '720p',
        'audio_formats': ['mp3', 'aac'],
    },
    'facebook': {
        'name': 'Facebook',
        'domains': ['facebook.com', 'fb.watch'],
        'supports_playlists': False,
        'supports_live': True,
        'max_quality': '1080p',
        'audio_formats': ['mp3', 'aac'],
    },
    'vimeo': {
        'name': 'Vimeo',
        'domains': ['vimeo.com'],
        'supports_playlists': True,
        'supports_live': False,
        'max_quality': '4k',
        'audio_formats': ['mp3', 'aac', 'flac'],
    },
    'twitch': {
        'name': 'Twitch',
        'domains': ['twitch.tv'],
        'supports_playlists': False,
        'supports_live': True,
        'max_quality': '1080p',
        'audio_formats': ['mp3', 'aac'],
    },
    'dailymotion': {
        'name': 'Dailymotion',
        'domains': ['dailymotion.com'],
        'supports_playlists': True,
        'supports_live': False,
        'max_quality': '1080p',
        'audio_formats': ['mp3', 'aac'],
    },
}

class Config:
    """Configuration manager for the video downloader."""

    def __init__(self, config_file='config.ini'):
        self.config_file = Path(config_file)
        self.settings = DEFAULT_CONFIG.copy()
        self.load_config()

    def load_config(self):
        """Load configuration from file."""
        if self.config_file.exists():
            try:
                import configparser
                config = configparser.ConfigParser()
                config.read(self.config_file)

                # Update settings from config file
                for section in config.sections():
                    for key, value in config[section].items():
                        if key in self.settings:
                            # Convert string values to appropriate types
                            if isinstance(self.settings[key], bool):
                                self.settings[key] = config.getboolean(section, key)
                            elif isinstance(self.settings[key], int):
                                self.settings[key] = config.getint(section, key)
                            elif isinstance(self.settings[key], float):
                                self.settings[key] = config.getfloat(section, key)
                            else:
                                self.settings[key] = value

            except Exception as e:
                print(f"Warning: Could not load config file: {e}")

    def save_config(self):
        """Save current configuration to file."""
        try:
            import configparser
            config = configparser.ConfigParser()

            # Organize settings into sections
            config['Download'] = {
                'default_output_dir': self.settings['default_output_dir'],
                'default_quality': self.settings['default_quality'],
                'default_format': self.settings['default_format'],
                'default_audio_format': self.settings['default_audio_format'],
                'default_audio_quality': self.settings['default_audio_quality'],
                'continue_on_error': str(self.settings['continue_on_error']),
                'overwrite_existing': str(self.settings['overwrite_existing']),
                'total_downloads': str(self.settings['total_downloads']),
            }

            config['Network'] = {
                'max_retries': str(self.settings['max_retries']),
                'retry_delay': str(self.settings['retry_delay']),
                'timeout': str(self.settings['timeout']),
                'concurrent_downloads': str(self.settings['concurrent_downloads']),
            }

            config['GUI'] = {
                'window_width': str(self.settings['window_width']),
                'window_height': str(self.settings['window_height']),
                'theme': self.settings['theme'],
                'auto_paste_url': str(self.settings['auto_paste_url']),
            }

            with open(self.config_file, 'w') as f:
                config.write(f)

        except Exception as e:
            print(f"Warning: Could not save config file: {e}")

    def get(self, key, default=None):
        """Get a configuration value."""
        return self.settings.get(key, default)

    def set(self, key, value):
        """Set a configuration value."""
        self.settings[key] = value

    def get_quality_preset(self, quality):
        """Get quality preset format string."""
        return QUALITY_PRESETS.get(quality, QUALITY_PRESETS.get('Full HD', 'best'))

    def get_platform_info(self, domain):
        """Get platform information for a domain."""
        domain = domain.lower()
        for platform, info in PLATFORM_SETTINGS.items():
            if any(d in domain for d in info['domains']):
                return info
        return None

    def get_supported_platforms(self):
        """Get list of supported platforms."""
        return list(PLATFORM_SETTINGS.keys())

    def increment_download_count(self):
        """Increment the total download counter."""
        current_count = self.get('total_downloads', 0)
        self.set('total_downloads', current_count + 1)
        self.save_config()
        return current_count + 1

    def get_download_count(self):
        """Get the current total download count."""
        return self.get('total_downloads', 0)

    def reset_download_count(self):
        """Reset the download counter to zero."""
        self.set('total_downloads', 0)
        self.save_config()

# Global config instance
config = Config()

# Utility functions
def get_output_dir():
    """Get the configured output directory."""
    output_dir = Path(config.get('default_output_dir'))
    output_dir.mkdir(exist_ok=True)
    return output_dir

def get_filename_template():
    """Get the configured filename template."""
    return config.get('filename_template')

def is_supported_platform(url):
    """Check if a URL is from a supported platform."""
    from urllib.parse import urlparse
    try:
        domain = urlparse(url).netloc.lower()
        return config.get_platform_info(domain) is not None
    except:
        return False

if __name__ == "__main__":
    # Test configuration
    print("Video Downloader Configuration")
    print("=" * 40)
    print(f"Default output directory: {config.get('default_output_dir')}")
    print(f"Default quality: {config.get('default_quality')}")
    print(f"Default format: {config.get('default_format')}")
    print(f"Supported platforms: {', '.join(config.get_supported_platforms())}")

    # Test platform detection
    test_urls = [
        "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
        "https://www.tiktok.com/@user/video/123456789",
        "https://www.instagram.com/p/ABC123/",
        "https://twitter.com/user/status/123456789",
    ]

    print("\nPlatform Detection Test:")
    from urllib.parse import urlparse
    for url in test_urls:
        platform_info = config.get_platform_info(urlparse(url).netloc)
        if platform_info:
            print(f"  {url} -> {platform_info['name']}")
        else:
            print(f"  {url} -> Unknown platform")
