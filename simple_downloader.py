#!/usr/bin/env python3
"""
Simple Command-Line Video Downloader
Works without GUI - just enter URLs and download!
"""
import sys
import os
from pathlib import Path

# Add current directory to path
sys.path.append(os.getcwd())

from video_downloader import VideoDownloader

def main():
    """Main command-line interface."""
    print("🎬 Video Downloader - Command Line Version")
    print("=" * 50)
    print()
    
    # Initialize downloader
    downloader = VideoDownloader()
    downloader.output_dir = Path("downloads")
    downloader.output_dir.mkdir(exist_ok=True)
    
    print(f"📁 Output Directory: {downloader.output_dir.absolute()}")
    print()
    
    while True:
        print("Options:")
        print("1. Download single video")
        print("2. Download from file (batch)")
        print("3. Exit")
        print()
        
        choice = input("Choose option (1-3): ").strip()
        
        if choice == "1":
            download_single_video(downloader)
        elif choice == "2":
            download_from_file(downloader)
        elif choice == "3":
            print("👋 Goodbye!")
            break
        else:
            print("❌ Invalid choice. Please enter 1, 2, or 3.")
        
        print()

def download_single_video(downloader):
    """Download a single video."""
    print("\n📥 Single Video Download")
    print("-" * 30)
    
    url = input("Enter video URL: ").strip()
    if not url:
        print("❌ No URL entered.")
        return
    
    print("\nQuality options:")
    print("1. Full HD (1920×1080) - Landscape")
    print("2. HD (1280×720) - Landscape")
    quality_choice = input("Choose quality (1-2, default=1): ").strip()

    if quality_choice == "2":
        quality = "HD (1280×720)"
    else:
        quality = "Full HD (1920×1080)"
    
    audio_only = input("Audio only? (y/n, default=n): ").strip().lower() == 'y'
    
    print(f"\n[download] Starting download: {url}")
    print(f"[download] Quality: {quality}")
    print(f"[download] Audio Only: {audio_only}")
    print(f"[download] Output: {downloader.output_dir}")
    print()
    
    try:
        result = downloader.download_video(
            url,
            quality=quality,
            audio_only=audio_only,
            output_format="mp4"
        )
        
        if result.get('success'):
            title = result.get('title', 'Unknown')
            print(f"✅ [download] Downloaded: {title}")
            print("🎉 Download completed successfully!")
        else:
            error = result.get('error', 'Unknown error')
            print(f"❌ [error] Download failed: {error}")
            
    except Exception as e:
        print(f"❌ [error] Exception: {e}")

def download_from_file(downloader):
    """Download videos from a file."""
    print("\n📄 Batch Download from File")
    print("-" * 30)
    
    file_path = input("Enter file path (e.g., urls.txt): ").strip()
    if not file_path:
        print("❌ No file path entered.")
        return
    
    if not Path(file_path).exists():
        print(f"❌ File not found: {file_path}")
        return
    
    print("\nQuality options:")
    print("1. Full HD (1080p)")
    print("2. HD (720p)")
    quality_choice = input("Choose quality (1-2, default=2 for batch): ").strip()
    
    if quality_choice == "1":
        quality = "Full HD"
    else:
        quality = "HD"  # Default to HD for batch downloads (faster)
    
    audio_only = input("Audio only? (y/n, default=n): ").strip().lower() == 'y'
    
    print(f"\n[download] Starting batch download from: {file_path}")
    print(f"[download] Quality: {quality}")
    print(f"[download] Audio Only: {audio_only}")
    print(f"[download] Output: {downloader.output_dir}")
    print()
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            urls = [line.strip() for line in f if line.strip() and not line.strip().startswith('#')]
        
        total_urls = len(urls)
        print(f"[download] Found {total_urls} URLs to download")
        print()
        
        successful = 0
        failed = 0
        
        for i, url in enumerate(urls, 1):
            print(f"[download] Downloading item {i} of {total_urls}")
            print(f"[download] URL: {url}")
            
            try:
                result = downloader.download_video(
                    url,
                    quality=quality,
                    audio_only=audio_only,
                    output_format="mp4"
                )
                
                if result.get('success'):
                    successful += 1
                    title = result.get('title', 'Unknown')
                    print(f"✅ [download] Downloaded: {title}")
                else:
                    failed += 1
                    error = result.get('error', 'Unknown error')
                    print(f"❌ [error] Failed: {error}")
                    
            except Exception as e:
                failed += 1
                print(f"❌ [error] Exception: {e}")
            
            print()
        
        print(f"📊 [download] Batch download completed:")
        print(f"   ✅ Successful: {successful}")
        print(f"   ❌ Failed: {failed}")
        print(f"   📁 Total: {total_urls}")
        
    except Exception as e:
        print(f"❌ [error] Error reading file: {e}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Download interrupted by user. Goodbye!")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        input("Press Enter to exit...")
