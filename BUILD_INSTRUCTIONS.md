# 🛠️ Tools Downloader - Auto Build Instructions

## 🚀 Quick Start - Automatic Build

The `launcher.bat` file has been enhanced to automatically:
- ✅ Download and install FFmpeg
- ✅ Install all Python requirements
- ✅ Build `Tools_Downloader.exe`
- ✅ Set up the complete application

### **One-Click Build Process:**

1. **Double-click** `launcher.bat`
2. **Wait** for the automatic setup (5-10 minutes)
3. **Launch** the created `Tools_Downloader.exe`

That's it! The script handles everything automatically.

## 📋 What the Auto-Build Does

### **Step 1: Python Check**
- Verifies Python installation
- Detects Python version
- Sets up proper commands

### **Step 2: Dependencies**
- Upgrades pip to latest version
- Installs all requirements from `requirements.txt`
- Installs PyInstaller for executable building

### **Step 3: FFmpeg Setup**
- Downloads FFmpeg automatically from official source
- Extracts and configures FFmpeg
- Handles download failures gracefully

### **Step 4: File Verification**
- Checks all required application files
- Ensures build prerequisites are met

### **Step 5: Executable Build**
- Uses PyInstaller to create `Tools_Downloader.exe`
- Bundles all dependencies into single file
- Includes FFmpeg, icon, and configuration
- Creates windowed application (no console)

## 🎯 Build Options

### **Automatic Build (Recommended):**
```bash
# Run the enhanced launcher:
launcher.bat

# This will automatically build Tools_Downloader.exe
```

### **Quick Launch (If Already Built):**
```bash
# Use the quick launcher:
launch.bat

# This will launch existing Tools_Downloader.exe
# Or offer to build if not found
```

### **Manual Python Launch:**
```bash
# If you prefer Python:
python launcher.py

# Or direct GUI:
python gui.py
```

## 🔧 Advanced Configuration

### **Custom Build Settings:**
The build process uses `build_tools_downloader.spec` for advanced configuration:
- Optimized hidden imports
- Excluded unnecessary modules
- Custom data file inclusion
- Enhanced compression settings

### **Build Customization:**
Edit `build_tools_downloader.spec` to:
- Add/remove included files
- Modify hidden imports
- Change compression settings
- Adjust executable options

## 📁 File Structure After Build

```
Tools Downloader/
├── Tools_Downloader.exe     # ← Main executable (standalone)
├── launcher.bat             # Auto-build script
├── launch.bat               # Quick launch script
├── ffmpeg.exe               # FFmpeg binary
├── icon.ico                 # Application icon
├── config.ini               # Configuration file
├── gui.py                   # Source files (optional)
├── launcher.py              # Source files (optional)
├── video_downloader.py      # Source files (optional)
└── requirements.txt         # Dependencies list
```

## 🎉 Distribution

### **Share the Executable:**
- **Single File:** Just share `Tools_Downloader.exe`
- **Complete Package:** Share entire folder for full functionality
- **No Dependencies:** Recipients don't need Python installed

### **Executable Features:**
- ✅ **Standalone** - No Python installation required
- ✅ **Portable** - Run from any location
- ✅ **Bundled** - All dependencies included
- ✅ **Windowed** - Clean GUI interface
- ✅ **Optimized** - Fast startup and execution

## 🛠️ Troubleshooting

### **Build Fails:**
1. **Check Python:** Ensure Python 3.7+ is installed
2. **Check Internet:** FFmpeg download requires internet
3. **Check Permissions:** Run as administrator if needed
4. **Check Space:** Ensure sufficient disk space (500MB+)

### **FFmpeg Issues:**
1. **Manual Download:** Download from https://ffmpeg.org
2. **Place in Folder:** Put `ffmpeg.exe` in project folder
3. **Re-run Build:** Run `launcher.bat` again

### **Python Issues:**
1. **Install Python:** Download from https://python.org
2. **Add to PATH:** Check "Add Python to PATH" during install
3. **Restart Terminal:** Close and reopen command prompt

## 📞 Support

If you encounter issues:
1. **Check Error Messages:** Read the console output carefully
2. **Try Manual Steps:** Install dependencies manually if needed
3. **Use Python Mode:** Run with `python launcher.py` as fallback

## 🎨 Customization

### **Change App Name:**
Edit line 19 in `gui.py`:
```python
self.app_name = "🛠️ Your Custom Name"
```

### **Change Icon:**
Replace `icon.ico` with your custom icon file

### **Modify Settings:**
Edit `config.ini` for default preferences

**🚀 Enjoy your automatically built Tools Downloader executable!**
