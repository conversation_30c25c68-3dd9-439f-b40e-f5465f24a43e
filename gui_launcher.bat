@echo off
cd /d "%~dp0"

REM Tools Downloader - Silent GUI Launcher
REM Uses pythonw to run without command prompt window

REM Try pythonw first (windowless Python - best for GUI)
pythonw gui_direct.py
if errorlevel 1 (
    REM If pythonw not available, try python with hidden window
    python gui_direct.py
    if errorlevel 1 (
        REM If python fails, try py command
        py gui_direct.py
        if errorlevel 1 (
            REM All failed, show error message
            echo ERROR: Python not installed!
            echo Please install Python from https://python.org
            pause
        )
    )
)
