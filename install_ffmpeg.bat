@echo off
title FFmpeg Installer for Tools Downloader
echo.
echo ==========================================
echo     FFmpeg Installer
echo ==========================================
echo.

echo [INFO] Installing FFmpeg for Tools Downloader...
echo.

REM Check if ffmpeg.exe already exists
if exist "ffmpeg.exe" (
    echo [OK] FFmpeg already installed
    goto :end
)

echo [INFO] FFmpeg is required for audio extraction and format conversion
echo [INFO] This will download FFmpeg automatically
echo.

REM Download FFmpeg using PowerShell
echo [INFO] Downloading FFmpeg...
powershell -Command "& {Invoke-WebRequest -Uri 'https://github.com/yt-dlp/FFmpeg-Builds/releases/download/latest/ffmpeg-master-latest-win64-gpl.zip' -OutFile 'ffmpeg.zip'}"

if not exist "ffmpeg.zip" (
    echo [ERROR] Failed to download FFmpeg
    echo Please download manually from: https://ffmpeg.org/download.html
    pause
    exit /b 1
)

echo [INFO] Extracting FFmpeg...
powershell -Command "& {Expand-Archive -Path 'ffmpeg.zip' -DestinationPath 'ffmpeg_temp' -Force}"

REM Find and copy ffmpeg.exe
for /r "ffmpeg_temp" %%i in (ffmpeg.exe) do (
    copy "%%i" "ffmpeg.exe" >nul 2>&1
    if exist "ffmpeg.exe" (
        echo [OK] FFmpeg installed successfully
        goto :cleanup
    )
)

echo [ERROR] Could not find ffmpeg.exe in downloaded files
goto :cleanup

:cleanup
if exist "ffmpeg.zip" del "ffmpeg.zip"
if exist "ffmpeg_temp" rmdir /s /q "ffmpeg_temp"

:end
echo.
echo [SUCCESS] FFmpeg is ready for Tools Downloader
echo You can now use audio extraction and format conversion features
echo.
pause
