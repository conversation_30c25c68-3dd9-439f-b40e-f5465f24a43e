# -*- mode: python ; coding: utf-8 -*-
"""
PyInstaller spec file for Tools Downloader
This file provides advanced build configuration for creating Tools_Downloader.exe
"""

import os
import sys
from pathlib import Path

# Get the current directory
current_dir = Path.cwd()

# Define data files to include
data_files = []

# Add icon if it exists
if (current_dir / "icon.ico").exists():
    data_files.append(("icon.ico", "."))

# Add config file if it exists
if (current_dir / "config.ini").exists():
    data_files.append(("config.ini", "."))

# Add ffmpeg if it exists
if (current_dir / "ffmpeg.exe").exists():
    data_files.append(("ffmpeg.exe", "."))

# Hidden imports for all required modules
hidden_imports = [
    'tkinter',
    'tkinter.ttk',
    'tkinter.filedialog',
    'tkinter.messagebox',
    'tkinter.scrolledtext',
    'queue',
    'threading',
    'pathlib',
    'configparser',
    'requests',
    'yt_dlp',
    'yt_dlp.extractor',
    'yt_dlp.downloader',
    'yt_dlp.postprocessor',
    'tqdm',
    'json',
    'urllib',
    'urllib.request',
    'urllib.parse',
    'urllib.error',
    'http',
    'http.client',
    'ssl',
    'socket',
    'subprocess',
    'tempfile',
    'shutil',
    'glob',
    'fnmatch',
    'platform',
    'datetime',
    'time',
    'os',
    'sys',
    're',
    'math',
    'base64',
    'hashlib',
    'uuid',
    'collections',
    'itertools',
    'functools',
    'operator',
    'copy',
    'pickle',
    'gzip',
    'zipfile',
    'tarfile',
]

# Analysis configuration
a = Analysis(
    ['launcher.py'],
    pathex=[str(current_dir)],
    binaries=[],
    datas=data_files,
    hiddenimports=hidden_imports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
        'PIL',
        'cv2',
        'tensorflow',
        'torch',
        'sklearn',
        'jupyter',
        'notebook',
        'IPython',
        'pytest',
        'sphinx',
        'setuptools',
        'distutils',
        'wheel',
        'pip',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# Remove duplicate entries
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# Create the executable
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='Tools_Downloader',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # Set to False for windowed application
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico' if (current_dir / "icon.ico").exists() else None,
)
