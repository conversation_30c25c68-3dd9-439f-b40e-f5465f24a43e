@echo off
title Tools Downloader - Auto Setup & Build
cd /d "%~dp0"

echo.
echo ==========================================
echo     🛠️ Tools Downloader - Auto Builder
echo ==========================================
echo.
echo [INFO] This will automatically:
echo   ✅ Download and install FFmpeg
echo   ✅ Install all Python requirements
echo   ✅ Build Tools_Downloader.exe
echo   ✅ Set up complete application
echo.

REM Check if Tools_Downloader.exe already exists
if exist "Tools_Downloader.exe" (
    echo [FOUND] Tools_Downloader.exe already exists!
    echo.
    choice /c YN /m "Do you want to rebuild it? (Y/N)"
    if errorlevel 2 goto :launch_existing
    echo.
    echo [INFO] Rebuilding Tools_Downloader.exe...
    echo.
)

REM ==========================================
REM STEP 1: Check Python
REM ==========================================
echo [STEP 1/5] Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    py --version >nul 2>&1
    if errorlevel 1 (
        echo ❌ Python not found!
        echo.
        echo Please install Python from: https://python.org
        echo Make sure to check "Add Python to PATH" during installation
        pause
        exit /b 1
    )
    set CMD=py
    set PIP_CMD=py -m pip
) else (
    set CMD=python
    set PIP_CMD=pip
)

for /f "tokens=2" %%i in ('%CMD% --version 2^>^&1') do set PYTHON_VERSION=%%i
echo ✅ Python %PYTHON_VERSION% found
echo.

REM ==========================================
REM STEP 2: Install/Update pip and requirements
REM ==========================================
echo [STEP 2/5] Installing Python requirements...
echo [INFO] Upgrading pip...
%PIP_CMD% install --upgrade pip >nul 2>&1

echo [INFO] Installing requirements from requirements.txt...
if exist "requirements.txt" (
    %PIP_CMD% install -r requirements.txt
    if errorlevel 1 (
        echo ❌ Failed to install requirements
        echo [INFO] Trying alternative installation method...
        %CMD% -m pip install -r requirements.txt
        if errorlevel 1 (
            echo ❌ Could not install requirements. Please check your internet connection.
            pause
            exit /b 1
        )
    )
    echo ✅ Requirements installed successfully
) else (
    echo ⚠️ requirements.txt not found, installing core dependencies...
    %PIP_CMD% install yt-dlp requests tqdm
    echo ✅ Core dependencies installed
)

echo [INFO] Installing PyInstaller for executable building...
%PIP_CMD% install pyinstaller
if errorlevel 1 (
    echo ❌ Failed to install PyInstaller
    pause
    exit /b 1
)
echo ✅ PyInstaller installed successfully
echo.

REM ==========================================
REM STEP 3: Download and install FFmpeg
REM ==========================================
echo [STEP 3/5] Setting up FFmpeg...
if exist "ffmpeg.exe" (
    echo ✅ FFmpeg already installed
) else (
    echo [INFO] Downloading FFmpeg...
    echo [INFO] This may take a few minutes depending on your internet speed...

    powershell -Command "& {try { Invoke-WebRequest -Uri 'https://github.com/yt-dlp/FFmpeg-Builds/releases/download/latest/ffmpeg-master-latest-win64-gpl.zip' -OutFile 'ffmpeg.zip' -UseBasicParsing } catch { exit 1 }}"

    if not exist "ffmpeg.zip" (
        echo ❌ Failed to download FFmpeg
        echo [INFO] Trying alternative download method...
        powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri 'https://github.com/yt-dlp/FFmpeg-Builds/releases/download/latest/ffmpeg-master-latest-win64-gpl.zip' -OutFile 'ffmpeg.zip'}"

        if not exist "ffmpeg.zip" (
            echo ❌ Could not download FFmpeg automatically
            echo [INFO] Please download FFmpeg manually from: https://ffmpeg.org/download.html
            echo [INFO] Place ffmpeg.exe in this folder and run this script again
            pause
            exit /b 1
        )
    )

    echo [INFO] Extracting FFmpeg...
    powershell -Command "& {Expand-Archive -Path 'ffmpeg.zip' -DestinationPath 'ffmpeg_temp' -Force}"

    REM Find and copy ffmpeg.exe
    for /r "ffmpeg_temp" %%i in (ffmpeg.exe) do (
        copy "%%i" "ffmpeg.exe" >nul 2>&1
        if exist "ffmpeg.exe" (
            echo ✅ FFmpeg installed successfully
            goto :cleanup_ffmpeg
        )
    )

    echo ❌ Could not find ffmpeg.exe in downloaded files
    goto :cleanup_ffmpeg

    :cleanup_ffmpeg
    if exist "ffmpeg.zip" del "ffmpeg.zip" >nul 2>&1
    if exist "ffmpeg_temp" rmdir /s /q "ffmpeg_temp" >nul 2>&1
)
echo.

REM ==========================================
REM STEP 4: Verify core files
REM ==========================================
echo [STEP 4/5] Verifying application files...
set MISSING_FILES=0

if not exist "gui.py" (
    echo ❌ gui.py missing!
    set MISSING_FILES=1
)
if not exist "launcher.py" (
    echo ❌ launcher.py missing!
    set MISSING_FILES=1
)
if not exist "video_downloader.py" (
    echo ❌ video_downloader.py missing!
    set MISSING_FILES=1
)

if %MISSING_FILES%==1 (
    echo ❌ Critical application files are missing!
    pause
    exit /b 1
)

echo ✅ All application files verified
echo.

REM ==========================================
REM STEP 5: Build Tools_Downloader.exe
REM ==========================================
echo [STEP 5/5] Building Tools_Downloader.exe...
echo [INFO] This may take several minutes...
echo [INFO] PyInstaller is analyzing dependencies and creating executable...
echo.

REM Create build directory if it doesn't exist
if not exist "build" mkdir build
if not exist "dist" mkdir dist

REM Clean previous builds
if exist "dist\Tools_Downloader.exe" del "dist\Tools_Downloader.exe" >nul 2>&1
if exist "Tools_Downloader.exe" del "Tools_Downloader.exe" >nul 2>&1

echo [INFO] Starting PyInstaller build process...

REM Check if custom spec file exists and use it for better build control
if exist "build_tools_downloader.spec" (
    echo [INFO] Using custom spec file for optimized build...
    %CMD% -m PyInstaller ^
        --distpath="." ^
        --workpath="build" ^
        --clean ^
        build_tools_downloader.spec
) else (
    echo [INFO] Using standard PyInstaller build...
    REM Build the executable with optimized settings
    %CMD% -m PyInstaller ^
        --onefile ^
        --windowed ^
        --name="Tools_Downloader" ^
        --icon="icon.ico" ^
        --add-data="icon.ico;." ^
        --add-data="config.ini;." ^
        --add-data="ffmpeg.exe;." ^
        --hidden-import="tkinter" ^
        --hidden-import="tkinter.ttk" ^
        --hidden-import="tkinter.filedialog" ^
        --hidden-import="tkinter.messagebox" ^
        --hidden-import="queue" ^
        --hidden-import="threading" ^
        --hidden-import="pathlib" ^
        --hidden-import="configparser" ^
        --hidden-import="requests" ^
        --hidden-import="yt_dlp" ^
        --hidden-import="tqdm" ^
        --distpath="." ^
        --workpath="build" ^
        --specpath="build" ^
        launcher.py
)

if errorlevel 1 (
    echo.
    echo ❌ Build failed!
    echo [INFO] Trying alternative build method...
    echo.

    REM Try simpler build without some options
    %CMD% -m PyInstaller ^
        --onefile ^
        --name="Tools_Downloader" ^
        --distpath="." ^
        launcher.py

    if errorlevel 1 (
        echo ❌ Alternative build also failed!
        echo [INFO] Please check the error messages above
        echo [INFO] You can still run the application with: %CMD% launcher.py
        pause
        goto :launch_python
    )
)

REM Check if executable was created
if exist "Tools_Downloader.exe" (
    echo.
    echo ✅ SUCCESS! Tools_Downloader.exe has been built successfully!
    echo.
    echo [INFO] Cleaning up build files...
    if exist "build" rmdir /s /q "build" >nul 2>&1
    if exist "dist" rmdir /s /q "dist" >nul 2>&1
    if exist "Tools_Downloader.spec" del "Tools_Downloader.spec" >nul 2>&1
    if exist "__pycache__" rmdir /s /q "__pycache__" >nul 2>&1

    echo ✅ Build cleanup completed
    echo.
    echo ==========================================
    echo     🎉 BUILD COMPLETE!
    echo ==========================================
    echo.
    echo ✅ Tools_Downloader.exe is ready to use!
    echo ✅ FFmpeg is installed and configured
    echo ✅ All dependencies are bundled
    echo.
    echo [INFO] You can now:
    echo   • Double-click Tools_Downloader.exe to run
    echo   • Share the .exe file with others
    echo   • No Python installation required on target machines
    echo.

    choice /c YN /m "Do you want to launch Tools_Downloader.exe now? (Y/N)"
    if errorlevel 2 goto :end

    echo [INFO] Launching Tools_Downloader.exe...
    start "" "Tools_Downloader.exe"
    goto :end

) else (
    echo ❌ Executable was not created successfully
    echo [INFO] You can still run the application with Python
    goto :launch_python
)

:launch_existing
echo [INFO] Launching existing Tools_Downloader.exe...
start "" "Tools_Downloader.exe"
goto :end

:launch_python
echo.
echo [INFO] Launching Tools Downloader with Python...
echo.
%CMD% launcher.py
goto :end

:end
echo.
echo [INFO] Setup and build process completed!
echo [INFO] Thank you for using Tools Downloader!
echo.
pause
