@echo off
title Tools Downloader - Build Test
cd /d "%~dp0"

echo.
echo ==========================================
echo     🧪 Tools Downloader - Build Test
echo ==========================================
echo.
echo [INFO] This script tests the build environment
echo [INFO] and verifies all components are ready
echo.

REM Test 1: Python Installation
echo [TEST 1/5] Testing Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    py --version >nul 2>&1
    if errorlevel 1 (
        echo ❌ FAIL: Python not found
        echo [INFO] Install Python from: https://python.org
        goto :test_failed
    ) else (
        echo ✅ PASS: Python found (py command)
        set CMD=py
    )
) else (
    echo ✅ PASS: Python found (python command)
    set CMD=python
)

REM Test 2: Required Files
echo [TEST 2/5] Testing required files...
set MISSING=0
if not exist "gui.py" (
    echo ❌ MISSING: gui.py
    set MISSING=1
)
if not exist "launcher.py" (
    echo ❌ MISSING: launcher.py
    set MISSING=1
)
if not exist "video_downloader.py" (
    echo ❌ MISSING: video_downloader.py
    set MISSING=1
)
if not exist "requirements.txt" (
    echo ❌ MISSING: requirements.txt
    set MISSING=1
)

if %MISSING%==1 (
    echo ❌ FAIL: Missing required files
    goto :test_failed
) else (
    echo ✅ PASS: All required files present
)

REM Test 3: Internet Connection
echo [TEST 3/5] Testing internet connection...
ping -n 1 google.com >nul 2>&1
if errorlevel 1 (
    echo ⚠️ WARNING: No internet connection detected
    echo [INFO] Internet required for FFmpeg download and pip installs
) else (
    echo ✅ PASS: Internet connection available
)

REM Test 4: Disk Space
echo [TEST 4/5] Testing disk space...
for /f "tokens=3" %%a in ('dir /-c ^| find "bytes free"') do set FREE_SPACE=%%a
if %FREE_SPACE% LSS 500000000 (
    echo ⚠️ WARNING: Low disk space (less than 500MB free)
    echo [INFO] Build process requires at least 500MB free space
) else (
    echo ✅ PASS: Sufficient disk space available
)

REM Test 5: Write Permissions
echo [TEST 5/5] Testing write permissions...
echo test > test_write.tmp 2>nul
if exist "test_write.tmp" (
    del test_write.tmp >nul 2>&1
    echo ✅ PASS: Write permissions available
) else (
    echo ❌ FAIL: No write permissions in current directory
    echo [INFO] Try running as administrator
    goto :test_failed
)

echo.
echo ==========================================
echo     ✅ ALL TESTS PASSED!
echo ==========================================
echo.
echo [INFO] Your system is ready for building Tools_Downloader.exe
echo [INFO] You can now run: launcher.bat
echo.
choice /c YN /m "Do you want to start the build process now? (Y/N)"
if errorlevel 2 goto :end

echo.
echo [INFO] Starting build process...
call launcher.bat
goto :end

:test_failed
echo.
echo ==========================================
echo     ❌ TESTS FAILED!
echo ==========================================
echo.
echo [INFO] Please fix the issues above before building
echo [INFO] Check BUILD_INSTRUCTIONS.md for help
echo.
goto :end

:end
echo.
echo [INFO] Build test completed!
pause
