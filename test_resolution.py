#!/usr/bin/env python3
"""
Test script to verify resolution targeting works correctly
"""

import sys
from pathlib import Path
from video_downloader import VideoDownloader

def test_resolution_targeting():
    """Test the enhanced resolution targeting functionality."""
    print("🧪 Testing Enhanced Resolution Targeting")
    print("=" * 50)
    
    # Initialize downloader
    downloader = VideoDownloader("test_downloads")
    
    # Test URLs (using short, safe videos for testing)
    test_urls = [
        "https://www.youtube.com/watch?v=dQw4w9WgXcQ",  # Rick Roll (safe test video)
        # Add more test URLs as needed
    ]
    
    # Test different quality settings
    quality_tests = [
        "Full HD (1920×1080)",
        "HD (1280×720)",
        "Full HD",  # Legacy format
        "HD",       # Legacy format
    ]
    
    print("🎯 Testing Quality Format Strings:")
    print("-" * 30)
    
    for quality in quality_tests:
        format_string = downloader._get_quality_format(quality)
        print(f"Quality: {quality}")
        print(f"Format: {format_string}")
        print()
    
    print("✅ Format string test completed!")
    print()
    
    # Test actual video info extraction (without downloading)
    print("📋 Testing Video Info Extraction:")
    print("-" * 30)
    
    for url in test_urls:
        print(f"Testing URL: {url}")
        try:
            info = downloader.get_video_info(url)
            if 'error' not in info:
                print(f"✅ Title: {info.get('title', 'Unknown')}")
                print(f"✅ Platform: {info.get('platform', 'Unknown')}")
                print(f"✅ Available formats: {info.get('formats', 0)}")
            else:
                print(f"❌ Error: {info['error']}")
        except Exception as e:
            print(f"❌ Exception: {str(e)}")
        print()
    
    print("🎉 Resolution targeting test completed!")
    print()
    print("📝 Notes:")
    print("- Format strings now target exact resolutions (1920×1080, 1280×720)")
    print("- LANDSCAPE PREFERENCE: Avoids portrait videos like 608×1080")
    print("- Width filters ensure proper aspect ratios (width>=1280 for 1080p)")
    print("- Fallback logic ensures downloads work even if exact resolution unavailable")
    print("- Enhanced quality reporting shows actual downloaded resolution")
    print("- GUI now displays clear resolution information")
    print()
    print("🚫 PORTRAIT VIDEO HANDLING:")
    print("- Old logic: best[height=1080] would accept 608×1080 (portrait)")
    print("- New logic: best[height=1080][width>=1280] rejects narrow videos")
    print("- Result: Only landscape 1080p videos are selected for Full HD")

def test_format_priority():
    """Test format selection priority."""
    print("🔍 Testing Format Selection Priority")
    print("=" * 40)

    downloader = VideoDownloader()

    test_cases = [
        ("Full HD (1920×1080)", "Should prioritize 1920×1080, prefer landscape, avoid portrait"),
        ("HD (1280×720)", "Should prioritize 1280×720, prefer landscape, avoid portrait"),
        ("Unknown Quality", "Should default to Full HD targeting with landscape preference"),
    ]

    for quality, expected in test_cases:
        format_string = downloader._get_quality_format(quality)
        print(f"Input: {quality}")
        print(f"Expected: {expected}")
        print(f"Format: {format_string}")
        print(f"✅ Contains width targeting: {'width=' in format_string}")
        print(f"✅ Contains height targeting: {'height=' in format_string}")
        print(f"✅ Contains landscape preference: {'width>=' in format_string}")
        print(f"✅ Avoids portrait videos: {'width>=1280' in format_string or 'width>=1000' in format_string}")
        print()

if __name__ == "__main__":
    print("🛠️ Tools Downloader - Resolution Testing")
    print("=" * 50)
    print()
    
    try:
        test_format_priority()
        print()
        test_resolution_targeting()
        
        print("🎯 All tests completed successfully!")
        print()
        print("🚀 Ready to rebuild Tools_Downloader.exe with enhanced resolution targeting!")
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        sys.exit(1)
