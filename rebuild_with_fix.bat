@echo off
title Rebuild with Portrait Video Fix
cd /d "%~dp0"

echo.
echo ==========================================
echo   🔧 Rebuilding with Portrait Video Fix
echo ==========================================
echo.
echo [INFO] This rebuild includes the fix for:
echo   ❌ Problem: Getting 608×1080 when selecting Full HD
echo   ✅ Solution: Landscape preference with width filters
echo.

REM Check if Tools_Downloader.exe exists
if exist "Tools_Downloader.exe" (
    echo [FOUND] Current Tools_Downloader.exe
    echo [INFO] This will be rebuilt with the portrait video fix
    echo.
)

echo [INFO] Starting rebuild process...
echo.

REM Run the launcher to rebuild
call launcher.bat

echo.
echo ==========================================
echo     🎉 REBUILD COMPLETE!
echo ==========================================
echo.
echo ✅ Portrait video fix has been applied!
echo.
echo 🔧 What's Fixed:
echo   • No more 608×1080 when selecting Full HD
echo   • Width filters ensure landscape videos
echo   • Better aspect ratio targeting
echo   • Improved fallback logic
echo.
echo 🧪 Test Instructions:
echo   1. Try the same video that gave you 608×1080
echo   2. Select "Full HD (1920×1080)"
echo   3. You should now get proper landscape resolution
echo   4. If 1920×1080 isn't available, you'll get 720p instead
echo.
echo 🎯 Expected Results:
echo   • 1920×1080 (if available)
echo   • 1600×1080+ (wide format if available)
echo   • 1280×1080+ (standard format if available)
echo   • 720p (fallback if no good 1080p)
echo   • NO MORE 608×1080 portrait videos!
echo.
pause
