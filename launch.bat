@echo off
title Tools Downloader - Quick Launch
cd /d "%~dp0"

REM Quick launcher for Tools_Downloader.exe
REM This file provides a simple way to launch the application

echo.
echo 🛠️ Tools Downloader - Quick Launch
echo ===================================
echo.

REM Check if Tools_Downloader.exe exists
if exist "Tools_Downloader.exe" (
    echo ✅ Found Tools_Downloader.exe
    echo [INFO] Launching Tools Downloader...
    echo.
    start "" "Tools_Downloader.exe"
    echo ✅ Tools Downloader launched successfully!
    echo.
    echo [INFO] You can close this window now.
    timeout /t 2 >nul
    exit /b 0
) else (
    echo ❌ Tools_Downloader.exe not found!
    echo.
    echo [INFO] To build the executable, run: launcher.bat
    echo [INFO] Or run with Python: python launcher.py
    echo.
    choice /c YN /m "Do you want to run the build process now? (Y/N)"
    if errorlevel 2 goto :end
    
    echo.
    echo [INFO] Starting build process...
    call launcher.bat
    goto :end
)

:end
echo.
pause
