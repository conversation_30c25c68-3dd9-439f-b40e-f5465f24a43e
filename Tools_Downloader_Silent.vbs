Set objShell = CreateObject("WScript.Shell")
Set objFSO = CreateObject("Scripting.FileSystemObject")

' Get the directory where this script is located
strScriptPath = objFSO.GetParentFolderName(WScript.ScriptFullName)

' Change to the script directory
objShell.CurrentDirectory = strScriptPath

' Check if Python is available and launch GUI silently
On Error Resume Next

' Try python command first
objShell.Run "python --version", 0, True
If Err.Number = 0 Then
    ' Python found, launch GUI
    objShell.Run "python gui.py", 0, False
Else
    ' Try py command
    Err.Clear
    objShell.Run "py --version", 0, True
    If Err.Number = 0 Then
        ' py found, launch GUI
        objShell.Run "py gui.py", 0, False
    Else
        ' Python not found, show error
        MsgBox "Python is not installed or not found in PATH." & vbCrLf & vbCrLf & "Please install Python from https://python.org", vbCritical, "Tools Downloader - Error"
    End If
End If
