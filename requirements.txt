# Video Downloader Tool Requirements
# Install with: pip install -r requirements.txt

# Core video downloading library
yt-dlp>=2023.12.30

# HTTP requests library
requests>=2.31.0

# Optional: Enhanced progress bars for CLI
tqdm>=4.66.0

# Optional: Better configuration file handling
configparser>=5.3.0

# Optional: Enhanced JSON handling
ujson>=5.8.0

# Optional: Better date/time handling
python-dateutil>=2.8.2

# Optional: Enhanced URL parsing
urllib3>=2.1.0

# Development dependencies (optional)
# Uncomment if you want to contribute to the project

# pytest>=7.4.0
# pytest-cov>=4.1.0
# black>=23.12.0
# flake8>=6.1.0
# mypy>=1.8.0

# Platform-specific dependencies
# These are automatically handled by yt-dlp, but listed for reference:

# FFmpeg (required for audio extraction and format conversion)
# - Windows: Download from https://ffmpeg.org/download.html
# - macOS: brew install ffmpeg
# - Linux: sudo apt-get install ffmpeg (Ubuntu/Debian) or equivalent

# Note: tkinter is included with Python on most systems
# If tkinter is missing (some Linux distributions):
# - Ubuntu/Debian: sudo apt-get install python3-tk
# - CentOS/RHEL: sudo yum install tkinter
# - Arch Linux: sudo pacman -S tk
