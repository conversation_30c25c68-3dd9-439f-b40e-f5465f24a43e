#!/usr/bin/env python3
"""
Silent GUI launcher - no console window
The .pyw extension prevents console window from appearing
"""
import sys
import os
from pathlib import Path

# Change to script directory
script_dir = Path(__file__).parent
os.chdir(script_dir)
sys.path.insert(0, str(script_dir))

try:
    # Import and run GUI directly
    from gui import main as gui_main
    gui_main()
except Exception:
    # Silent fail - no error messages
    pass
