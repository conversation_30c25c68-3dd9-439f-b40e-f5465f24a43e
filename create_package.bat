@echo off
title Tools Downloader - Package Creator
cd /d "%~dp0"

echo.
echo ==========================================
echo   📦 Tools Downloader Package Creator
echo ==========================================
echo.
echo [INFO] Creating complete distribution package...
echo.

REM Check if Tools_Downloader.exe exists
if not exist "Tools_Downloader.exe" (
    echo ❌ Tools_Downloader.exe not found!
    echo [INFO] Please run launcher.bat first to build the executable
    pause
    exit /b 1
)

REM Create package directory
set PACKAGE_DIR=Tools_Downloader_Complete_Package
if exist "%PACKAGE_DIR%" rmdir /s /q "%PACKAGE_DIR%"
mkdir "%PACKAGE_DIR%"

echo [STEP 1/4] Copying main executable...
copy "Tools_Downloader.exe" "%PACKAGE_DIR%\" >nul
if exist "%PACKAGE_DIR%\Tools_Downloader.exe" (
    echo ✅ Tools_Downloader.exe copied
) else (
    echo ❌ Failed to copy executable
    pause
    exit /b 1
)

echo [STEP 2/4] Copying essential files...

REM Copy FFmpeg if it exists
if exist "ffmpeg.exe" (
    copy "ffmpeg.exe" "%PACKAGE_DIR%\" >nul
    echo ✅ ffmpeg.exe copied (for audio extraction)
) else (
    echo ⚠️ ffmpeg.exe not found (audio features may not work)
)

REM Copy icon if it exists
if exist "icon.ico" (
    copy "icon.ico" "%PACKAGE_DIR%\" >nul
    echo ✅ icon.ico copied
) else (
    echo ⚠️ icon.ico not found
)

REM Copy config if it exists
if exist "config.ini" (
    copy "config.ini" "%PACKAGE_DIR%\" >nul
    echo ✅ config.ini copied
) else (
    echo ⚠️ config.ini not found
)

echo [STEP 3/4] Creating documentation...

REM Create README for the package
echo Creating README.txt for users...
(
echo ==========================================
echo     🛠️ TOOLS DOWNLOADER - USER GUIDE
echo ==========================================
echo.
echo 🚀 QUICK START:
echo   1. Double-click "Tools_Downloader.exe" to run
echo   2. Paste a video URL in the URL field
echo   3. Choose quality and options
echo   4. Click "Download" button
echo   5. Videos will be saved to "downloads" folder
echo.
echo 📁 PACKAGE CONTENTS:
echo   • Tools_Downloader.exe  - Main application
echo   • ffmpeg.exe           - Audio extraction tool
echo   • icon.ico             - Application icon
echo   • config.ini           - Settings file
echo   • README.txt           - This guide
echo.
echo ✅ FEATURES:
echo   • Download videos from YouTube, TikTok, Instagram, etc.
echo   • Extract audio only (MP3 format)
echo   • Choose video quality (1080p, 720p)
echo   • Batch download from text files
echo   • Progress tracking and logs
echo   • Auto-completion notifications
echo.
echo 🎯 SUPPORTED PLATFORMS:
echo   • YouTube (videos, playlists, live streams)
echo   • TikTok (videos)
echo   • Instagram (videos, stories)
echo   • And many more platforms
echo.
echo ⚙️ SYSTEM REQUIREMENTS:
echo   • Windows 7/8/10/11 (64-bit)
echo   • Internet connection (for downloading)
echo   • No Python installation required
echo.
echo 🛠️ TROUBLESHOOTING:
echo   • If antivirus blocks the app, add it to exceptions
echo   • For audio extraction issues, ensure ffmpeg.exe is present
echo   • Check internet connection if downloads fail
echo.
echo 📞 SUPPORT:
echo   • This is a standalone application
echo   • All dependencies are bundled
echo   • No installation required
echo.
echo 🎉 ENJOY YOUR TOOLS DOWNLOADER!
echo.
echo Version: Standalone Executable
echo Built with: Python + PyInstaller
echo ==========================================
) > "%PACKAGE_DIR%\README.txt"

echo ✅ README.txt created

REM Create a quick launcher batch file
echo Creating Quick_Launch.bat...
(
echo @echo off
echo title Tools Downloader
echo cd /d "%%~dp0"
echo.
echo 🛠️ Tools Downloader - Quick Launch
echo ===================================
echo.
echo [INFO] Starting Tools Downloader...
echo.
echo If the application doesn't start, check:
echo   • Windows Defender/Antivirus settings
echo   • File permissions
echo   • Internet connection
echo.
echo Starting application...
echo.
start "" "Tools_Downloader.exe"
echo.
echo ✅ Tools Downloader launched!
echo [INFO] You can close this window now.
echo.
timeout /t 3 >nul
) > "%PACKAGE_DIR%\Quick_Launch.bat"

echo ✅ Quick_Launch.bat created

echo [STEP 4/4] Creating downloads folder...
mkdir "%PACKAGE_DIR%\downloads" >nul 2>&1
echo ✅ downloads folder created

echo.
echo ==========================================
echo     ✅ PACKAGE CREATION COMPLETE!
echo ==========================================
echo.
echo 📦 Package Location: %PACKAGE_DIR%\
echo.
echo 📁 Package Contents:
dir "%PACKAGE_DIR%" /b
echo.
echo 🎯 DISTRIBUTION OPTIONS:
echo.
echo   Option A - Share Folder:
echo     • Copy the entire "%PACKAGE_DIR%" folder
echo     • Share via USB, network, or cloud storage
echo     • Recipients can use immediately
echo.
echo   Option B - Create ZIP:
echo     • Right-click "%PACKAGE_DIR%" folder
echo     • Select "Send to" → "Compressed folder"
echo     • Share the ZIP file
echo.
echo   Option C - Just the EXE:
echo     • Share only "Tools_Downloader.exe"
echo     • Simplest option (audio features may be limited)
echo.
echo 🚀 READY FOR DISTRIBUTION!
echo.
choice /c YN /m "Do you want to open the package folder now? (Y/N)"
if errorlevel 2 goto :end

echo [INFO] Opening package folder...
start "" "%PACKAGE_DIR%"

:end
echo.
echo [INFO] Package creation completed!
echo [INFO] Your Tools Downloader is ready to share!
echo.
pause
