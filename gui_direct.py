#!/usr/bin/env python3
"""
Direct GUI Launcher for Tools Downloader
Launches the GUI directly without showing command prompt or menu
"""

import sys
import os

def main():
    """Launch GUI directly."""
    try:
        # Import and run GUI
        from gui import main as gui_main
        gui_main()
    except ImportError as e:
        print(f"Error importing GUI: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"Error running GUI: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
